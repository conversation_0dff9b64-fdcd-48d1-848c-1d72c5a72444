{"cli": {"version": ">= 5.2.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"android": {"buildType": "aab", "gradleCommand": ":app:bundleRelease"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "./service-account-key.json", "track": "internal", "releaseStatus": "inProgress"}}}}