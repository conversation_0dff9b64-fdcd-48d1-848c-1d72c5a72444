// Auto-applied by with-android-okhttp config plugin
dependencies {
    implementation "com.squareup.okhttp3:okhttp:4.12.0"
    implementation "com.squareup.okhttp3:logging-interceptor:4.12.0"
    implementation "com.squareup.okhttp3:okhttp-urlconnection:4.12.0"
}

// Force resolution in case React Native or other libs pull old versions
configurations.all {
    resolutionStrategy {
        force "com.squareup.okhttp3:okhttp:4.12.0"
        force "com.squareup.okhttp3:logging-interceptor:4.12.0"
        force "com.squareup.okhttp3:okhttp-urlconnection:4.12.0"
    }
}
