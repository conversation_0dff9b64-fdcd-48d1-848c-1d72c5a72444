{"name": "vega", "version": "3.2.4", "private": true, "scripts": {"android": "expo run:android", "android:build": "eas build --platform android", "android:setup": "node scripts/setup-expo-android.js", "android:backup": "node scripts/backup-android-files.js", "ios": "expo run:ios", "lint": "eslint .", "start": "expo start", "test": "jest", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean", "postinstall": "patch-package"}, "overrides": {"expo-font": "13.0.4"}, "dependencies": {"@8man/react-native-media-console": "^2.2.4-alpha.25", "@dr.pogodin/react-native-fs": "^2.34.0", "@expo/config-plugins": "~9.0.0", "@gorhom/bottom-sheet": "^4.6.4", "@himanshu8443/react-native-apk-installer": "github:himanshu8443/react-native-apk-installer", "@notifee/react-native": "^9.1.8", "@react-native-community/blur": "^4.4.1", "@react-native-firebase/analytics": "^23.0.0", "@react-native-firebase/app": "^23.0.0", "@react-native-firebase/crashlytics": "^23.0.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@shopify/flash-list": "1.7.3", "@tanstack/react-query": "^5.81.5", "axios": "^1.6.8", "babel-plugin-module-resolver": "^5.0.2", "cheerio": "^1.0.0-rc.12", "expo": "^52.0.0", "expo-application": "~6.0.2", "expo-blur": "~14.0.3", "expo-brightness": "~13.0.3", "expo-build-properties": "~0.13.3", "expo-crypto": "~14.0.2", "expo-document-picker": "~13.0.3", "expo-intent-launcher": "~12.0.2", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "expo-video-thumbnails": "~9.0.3", "moti": "^0.28.1", "nativewind": "^2.0.11", "react": "18.3.1", "react-native": "0.76.9", "react-native-bootsplash": "^6.3.2", "react-native-edge-to-edge": "^1.6.2", "react-native-element-dropdown": "^2.12.0", "react-native-fullscreen-chz": "^4.0.13", "react-native-gesture-handler": "~2.20.2", "react-native-haptic-feedback": "^2.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv-storage": "^0.11.2", "react-native-orientation-locker": "^1.7.0", "react-native-permissions": "^4.1.5", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-video": "^6.16.1", "react-native-volume-manager": "^2.0.8", "react-native-webview": "13.12.5", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.81", "@react-native/eslint-config": "0.74.81", "@react-native/metro-config": "0.74.81", "@react-native/typescript-config": "0.74.81", "@types/lodash": "^4.17.16", "@types/react": "~18.3.12", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "patch-package": "^8.0.0", "prettier": "^3.0.0", "pretty-quick": "^4.1.1", "react-test-renderer": "18.2.0", "simple-git-hooks": "^2.12.1", "tailwindcss": "^3.3.2", "typescript": "~5.3.3"}, "engines": {"node": ">=18"}}